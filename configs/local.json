{"database": {"knowledge_graph_db": "data/knowledge_graph.db", "embeddings_db": "data/knowledge_graph_embeddings.db", "connection_timeout": 60, "max_retry_attempts": 5, "batch_size": 1000, "enable_wal_mode": true}, "storage": {"data_dir": "data", "sessions_dir": "data/sessions", "file_changes_dir": "data/file_changes", "file_edits_dir": "data/edits", "parser_results_dir": "data/parser_results", "models_dir": "models"}, "embedding": {"model_name": "all-MiniLM-L12-v2", "model_path": "models/all-MiniLM-L12-v2", "max_tokens": 230, "overlap_tokens": 30, "similarity_threshold": 0.2, "enable_optimization": false}, "parser": {"config_file": "src/parser/config/parsers.json", "build_directory": "src/parser/build"}, "web_search": {"api_key": "BSAjU5A0LBWnIcYSBAgzC5rnxsolAqs", "requests_per_minute": 60, "timeout": 30}, "web_scrap": {"timeout": 30, "max_retries": 3, "delay_between_retries": 1.0, "include_comments": true, "include_tables": true, "include_images": true, "include_links": true, "trafilatura_config": {}, "markdown_options": {"heading_style": "ATX", "bullets": "-", "wrap": true}}, "logging": {"level": "DEBUG", "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}", "log_file": "data/sutraknowledge.log"}, "llm": {"provider": "aws", "llama_model_id": "meta/llama-3.1-8b-instruct", "claude_model": "us.anthropic.claude-sonnet-4-20250514-v1:0", "gemini_model": "gemini-1.5-flash", "aws": {"model_id": "us.anthropic.claude-sonnet-4-20250514-v1:0", "access_key_id": "********************", "secret_access_key": "EBLJIAeYRTfQREZ2mOrSSH3Xc9qdC8HNjUj1xpdR", "region": "us-east-2"}, "anthropic": {"model_id": "us.anthropic.claude-sonnet-4-20250514-v1:0", "api_key": ""}, "gcp": {"api_key": "", "project_id": "YOUR_GCP_PROJECT_ID", "location": "YOUR_GCP_LOCATION", "llm_endpoint": "YOUR_GCP_LLM_ENDPOINT"}, "superllm": {"api_endpoint": "http://localhost:8000", "firebase_token": "", "default_model": "gpt-3.5-turbo", "default_provider": "openai"}}}
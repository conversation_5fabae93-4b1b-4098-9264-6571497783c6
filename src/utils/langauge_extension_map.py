"""
Supported languages configuration for AST Parser

This module contains the mapping of programming languages to their file extensions
and specific filenames that should be parsed.
"""

from typing import Dict, List
from tree_sitter_language_pack import SupportedLanguage

# Supported languages and their file extensions
LANGUAGE_EXTENSION_MAP: Dict[SupportedLanguage, List[str]] = {
    "actionscript": [".as", ".mxml"],
    "ada": [".ada", ".adb", ".ads"],
    "agda": [".agda"],
    "apex": [".cls", ".trigger"],
    "arduino": [".ino", ".pde"],
    "asm": [".asm", ".s", ".S"],
    "astro": [".astro"],
    "bash": [".sh", ".bash", ".zsh"],
    "beancount": [".beancount"],
    "bibtex": [".bib"],
    "bicep": [".bicep"],
    "bitbake": [".bb", ".bbappend", ".bbclass"],
    "c": [".c", ".h"],
    "cairo": [".cairo"],
    "capnp": [".capnp"],
    "chatito": [".chatito"],
    "clarity": [".clar"],
    "clojure": [".clj", ".cljs", ".cljc", ".edn"],
    "cmake": [".cmake", "CMakeLists.txt"],
    "comment": [],
    "commonlisp": [".lisp", ".lsp", ".cl"],
    "cpon": [".cpon"],
    "cpp": [".cpp", ".cxx", ".cc", ".C", ".hpp", ".hxx", ".hh", ".H"],
    "csharp": [".cs", ".csx"],
    "css": [".css"],
    "csv": [".csv"],
    "cuda": [".cu", ".cuh"],
    "d": [".d", ".di"],
    "dart": [".dart"],
    "dockerfile": ["Dockerfile", ".dockerfile"],
    "doxygen": [".dox"],
    "elisp": [".el"],
    "elixir": [".ex", ".exs"],
    "elm": [".elm"],
    "embeddedtemplate": [".et"],
    "erlang": [".erl", ".hrl"],
    "fennel": [".fnl"],
    "firrtl": [".fir"],
    "fish": [".fish"],
    "fortran": [".f", ".f90", ".f95", ".f03", ".f08", ".for", ".ftn"],
    "func": [".func"],
    "gdscript": [".gd"],
    "gitattributes": [".gitattributes"],
    "gitcommit": ["COMMIT_EDITMSG"],
    "gitignore": [".gitignore"],
    "gleam": [".gleam"],
    "glsl": [".glsl", ".vert", ".frag", ".geom", ".tesc", ".tese", ".comp"],
    "gn": [".gn", ".gni"],
    "go": [".go"],
    "gomod": ["go.mod"],
    "gosum": ["go.sum"],
    "groovy": [".groovy", ".gvy", ".gy", ".gsh"],
    "gstlaunch": [".gst"],
    "hack": [".hack", ".hh", ".php"],
    "hare": [".ha"],
    "haskell": [".hs", ".lhs"],
    "haxe": [".hx"],
    "hcl": [".hcl", ".tf", ".tfvars"],
    "heex": [".heex"],
    "hlsl": [".hlsl", ".fx", ".fxh"],
    "html": [".html", ".htm", ".xhtml"],
    "hyprlang": [".hl"],
    "ispc": [".ispc"],
    "janet": [".janet"],
    "java": [".java"],
    "javascript": [".js", ".mjs", ".cjs", ".jsx"],
    "jsdoc": [],
    "json": [".json"],
    "jsonnet": [".jsonnet", ".libsonnet"],
    "julia": [".jl"],
    "kconfig": ["Kconfig", "Config.in"],
    "kdl": [".kdl"],
    "kotlin": [".kt", ".kts"],
    "linkerscript": [".ld", ".lds"],
    "llvm": [".ll"],
    "lua": [".lua"],
    "luadoc": [],
    "luap": [],
    "luau": [".luau"],
    "magik": [".magik"],
    "make": ["Makefile", "makefile", ".mk", "GNUmakefile"],
    "markdown": [".md", ".markdown", ".mdown", ".mkd", ".mkdn"],
    "markdown_inline": [],
    "matlab": [".m", ".mlx"],
    "mermaid": [".mmd", ".mermaid"],
    "meson": ["meson.build", "meson_options.txt"],
    "ninja": [".ninja", "build.ninja"],
    "nix": [".nix"],
    "nqc": [".nqc"],
    "objc": [".m", ".mm"],
    "ocaml": [".ml", ".mli"],
    "ocaml_interface": [".mli"],
    "odin": [".odin"],
    "org": [".org"],
    "pascal": [".pas", ".pp", ".inc"],
    "pem": [".pem", ".crt", ".cer", ".key"],
    "perl": [".pl", ".pm", ".pod", ".t"],
    "pgn": [".pgn"],
    "php": [".php", ".php3", ".php4", ".php5", ".phtml"],
    "po": [".po", ".pot"],
    "pony": [".pony"],
    "powershell": [".ps1", ".psm1", ".psd1"],
    "printf": [],
    "prisma": [".prisma"],
    "properties": [".properties"],
    "proto": [".proto"],
    "psv": [".psv"],
    "puppet": [".pp"],
    "purescript": [".purs"],
    "pymanifest": ["MANIFEST.in"],
    "python": [".py", ".pyw", ".pyi", ".pyx"],
    "qmldir": ["qmldir"],
    "query": [".scm"],
    "r": [".r", ".R", ".rmd", ".Rmd"],
    "racket": [".rkt", ".rktl"],
    "rbs": [".rbs"],
    "re2c": [".re"],
    "readline": [],
    "requirements": ["requirements.txt", "requirements.in"],
    "ron": [".ron"],
    "rst": [".rst", ".rest"],
    "ruby": [".rb", ".rbw", ".rake", ".gemspec", "Rakefile", "Gemfile"],
    "rust": [".rs"],
    "scala": [".scala", ".sc"],
    "scheme": [".scm", ".ss"],
    "scss": [".scss"],
    "slang": [".slang"],
    "smali": [".smali"],
    "smithy": [".smithy"],
    "solidity": [".sol"],
    "sparql": [".sparql", ".rq"],
    "sql": [".sql"],
    "squirrel": [".nut"],
    "starlark": [".star", ".bzl", "BUILD", "WORKSPACE"],
    "svelte": [".svelte"],
    "swift": [".swift"],
    "tablegen": [".td"],
    "tcl": [".tcl", ".tk"],
    "test": [],
    "thrift": [".thrift"],
    "toml": [".toml"],
    "tsv": [".tsv"],
    "twig": [".twig"],
    "typescript": [".ts", ".tsx"],
    "typst": [".typ"],
    "udev": [".rules"],
    "ungrammar": [".ungram"],
    "uxntal": [".tal"],
    "v": [".v"],
    "verilog": [".v", ".vh", ".sv", ".svh"],
    "vhdl": [".vhd", ".vhdl"],
    "vim": [".vim", ".vimrc"],
    "vue": [".vue"],
    "wgsl": [".wgsl"],
    "xcompose": [".XCompose"],
    "xml": [".xml", ".xsd", ".xsl", ".xslt", ".wsdl", ".svg"],
    "yaml": [".yaml", ".yml"],
    "yuck": [".yuck"],
    "zig": [".zig"],
}

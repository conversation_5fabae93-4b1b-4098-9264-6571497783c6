"""
Available tools and capabilities for the Roadmap Agent (impact-aware planning)
"""

CAPABILITIES = """
- You have access to tools for semantic discovery, keyword/pattern search, structured code graph queries, listing workspace files, running terminal commands, and producing final results. You also have Sutra Memory to track progress and avoid redundant work.

- semantic_search: Discover relevant files/blocks by meaning when exact names are unknown.

- search_keyword: Find symbol usages/definitions and content patterns with regex/case/context. <PERSON><PERSON> searches to a limited set of file paths instead of the whole repo.

- database_search (minimal, agent-facing wrappers):
  - get_block_details(block_id): Returns block metadata (file_path, language, start/end line & column), parent block summary, and any incoming/outgoing connections whose snippet_lines overlap the block’s line range.
  - get_file_block_summary(file_id): Quick list of blocks in a file for local navigation (no content).
  - get_search_scope_by_import_graph(anchor_file_id, direction="both", max_depth=2): Returns a set of file paths based on the import graph to constrain search scope.
  Note: dependency/impact chain queries and project-wide connection aggregation run in the background and are not directly called by the agent.

- list_files: Inspect workspace directories when needed.

- terminal_commands: Run one-off commands with session reuse. If needed, prefix with `cd <path> && <command>` in a single call.

- completion: Present the final roadmap once ready and prior tool outcomes are confirmed.

Guidance:
- Start with semantic_search → call get_block_details for each discovered block to receive file/parent/connection-in-range context. For symbol usage, first call get_search_scope_by_import_graph to compute a small path set, then use search_keyword scoped to those paths. Keep results focused (5–25 items) and store important findings in Sutra Memory with exact file paths and line numbers. Always include <thinking> before tool calls and maintain one-tool-per-iteration.
"""

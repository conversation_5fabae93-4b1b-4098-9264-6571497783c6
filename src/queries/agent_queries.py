"""
Roadmap Agent Database Queries
Exposed queries for agent direct access + Background queries for auto-conversion.
Architecture: Agent gets actionable data, never sees raw IDs or database complexity.
"""

# ============================================================================
# BACKGROUND QUERIES - Auto-Conversion (Never Exposed to Agent)
# ============================================================================

GET_CODE_BLOCK_BY_ID = """
SELECT
    cb.id, cb.type, cb.name, cb.content, cb.start_line, cb.end_line,
    cb.start_col, cb.end_col, cb.parent_block_id,
    f.file_path, f.language, f.id as file_id,
    p.name as project_name, p.id as project_id
FROM code_blocks cb
JOIN files f ON cb.file_id = f.id
JOIN projects p ON f.project_id = p.id
WHERE cb.id = ?
"""

GET_IMPLEMENTATION_CONTEXT = """
SELECT
    cb.id, cb.type, cb.name, cb.start_line, cb.end_line,
    parent.name as parent_name, parent.type as parent_type,
    f.file_path, f.language
FROM code_blocks cb
LEFT JOIN code_blocks parent ON cb.parent_block_id = parent.id
JOIN files f ON cb.file_id = f.id
WHERE cb.file_id = ?
ORDER BY cb.start_line
"""

GET_EXTERNAL_CONNECTIONS_OVERLAPPING_RANGE = """
SELECT
    'incoming' as direction, ic.description, ic.technology_name, ic.snippet_lines
FROM incoming_connections ic
WHERE ic.file_id = ? AND (
    json_extract(ic.snippet_lines, '$[0]') BETWEEN ? AND ? OR
    json_extract(ic.snippet_lines, '$[1]') BETWEEN ? AND ? OR
    (json_extract(ic.snippet_lines, '$[0]') <= ? AND json_extract(ic.snippet_lines, '$[1]') >= ?)
)
UNION ALL
SELECT
    'outgoing' as direction, oc.description, oc.technology_name, oc.snippet_lines
FROM outgoing_connections oc
WHERE oc.file_id = ? AND (
    json_extract(oc.snippet_lines, '$[0]') BETWEEN ? AND ? OR
    json_extract(oc.snippet_lines, '$[1]') BETWEEN ? AND ? OR
    (json_extract(oc.snippet_lines, '$[0]') <= ? AND json_extract(oc.snippet_lines, '$[1]') >= ?)
)
ORDER BY direction, technology_name
LIMIT 15
"""

# ============================================================================
# EXPOSED QUERIES
# ============================================================================

GET_FILE_BY_ID = """
SELECT
    f.id, f.file_path, f.language, f.content, f.content_hash,
    p.name as project_name, p.id as project_id,
    COUNT(cb.id) as block_count
FROM files f
JOIN projects p ON f.project_id = p.id
LEFT JOIN code_blocks cb ON f.id = cb.file_id
WHERE f.id = ?
GROUP BY f.id
"""

GET_FILE_BLOCK_SUMMARY = """
SELECT
    cb.id, cb.type, cb.name, cb.start_line, cb.end_line, cb.parent_block_id
FROM code_blocks cb
WHERE cb.file_id = ?
ORDER BY cb.start_line
LIMIT 20
"""

GET_CHILD_BLOCKS = """
SELECT
    cb.id, cb.type, cb.name, cb.start_line, cb.end_line
FROM code_blocks cb
WHERE cb.parent_block_id = ?
ORDER BY cb.start_line
LIMIT 15
"""

GET_PARENT_BLOCK = """
SELECT
    cb.id, cb.type, cb.name, cb.start_line, cb.end_line,
    f.file_path
FROM code_blocks cb
JOIN files f ON cb.file_id = f.id
WHERE cb.id = (SELECT parent_block_id FROM code_blocks WHERE id = ?)
"""


GET_FILE_IMPORTS = """
SELECT
    r.import_content,
    f.file_path, f.language, p.name as project_name
FROM relationships r
JOIN files f ON r.target_id = f.id
JOIN projects p ON f.project_id = p.id
WHERE r.source_id = ?
ORDER BY f.file_path
LIMIT 15
"""

GET_DEPENDENCY_CHAIN = """
WITH RECURSIVE dep_chain(file_id, file_path, target_id, target_path, depth, path) AS (
    -- Base case: direct dependencies
    SELECT
        r.source_id, sf.file_path,
        r.target_id, tf.file_path,
        1, sf.file_path || ' → ' || tf.file_path
    FROM relationships r
    JOIN files sf ON r.source_id = sf.id
    JOIN files tf ON r.target_id = tf.id
    WHERE r.source_id = ?

    UNION ALL

    -- Recursive case: follow the chain
    SELECT
        dc.file_id, dc.file_path,
        r.target_id, tf.file_path,
        dc.depth + 1, dc.path || ' → ' || tf.file_path
    FROM dep_chain dc
    JOIN relationships r ON dc.target_id = r.source_id
    JOIN files tf ON r.target_id = tf.id
    WHERE dc.depth < ? AND dc.path NOT LIKE '%' || tf.file_path || '%'
)
SELECT file_id, file_path, target_id, target_path, depth, path
FROM dep_chain
ORDER BY depth, file_path
LIMIT 25
"""

# ============================================================================
# Others
# ============================================================================

GET_FILE_IMPACT_SCOPE = """
SELECT
    'importer' as relationship_type,
    f.file_path, f.language, p.name as project_name,
    r.import_content
FROM relationships r
JOIN files f ON r.source_id = f.id
JOIN projects p ON f.project_id = p.id
WHERE r.target_id = ?
UNION ALL
SELECT
    'dependency' as relationship_type,
    f.file_path, f.language, p.name as project_name,
    r.import_content
FROM relationships r
JOIN files f ON r.target_id = f.id
JOIN projects p ON f.project_id = p.id
WHERE r.source_id = ?
ORDER BY relationship_type, file_path
LIMIT 25
"""

GET_EXTERNAL_CONNECTIONS = """
SELECT
    'incoming' as direction, ic.description, ic.technology_name, ic.snippet_lines
FROM incoming_connections ic
WHERE ic.file_id = ?
UNION ALL
SELECT
    'outgoing' as direction, oc.description, oc.technology_name, oc.snippet_lines
FROM outgoing_connections oc
WHERE oc.file_id = ?
ORDER BY direction, technology_name
LIMIT 15
"""

GET_PROJECT_EXTERNAL_CONNECTIONS = """
SELECT
    f.file_path, f.language,
    COALESCE(ic.technology_name, oc.technology_name) as technology,
    COALESCE(ic.description, oc.description) as description,
    CASE WHEN ic.id IS NOT NULL THEN 'incoming' ELSE 'outgoing' END as direction
FROM files f
LEFT JOIN incoming_connections ic ON f.id = ic.file_id
LEFT JOIN outgoing_connections oc ON f.id = oc.file_id
WHERE f.project_id = ? AND (ic.id IS NOT NULL OR oc.id IS NOT NULL)
ORDER BY f.file_path, direction
LIMIT 25
"""

GET_CONNECTION_IMPACT = """
SELECT
    cm.connection_type, cm.description, cm.match_confidence,
    CASE
        WHEN ic.file_id = ? THEN 'receives_from'
        WHEN oc.file_id = ? THEN 'sends_to'
    END as impact_type,
    COALESCE(if2.file_path, of2.file_path) as other_file,
    COALESCE(ic2.technology_name, oc2.technology_name) as technology
FROM connection_mappings cm
LEFT JOIN incoming_connections ic ON cm.receiver_id = ic.id
LEFT JOIN outgoing_connections oc ON cm.sender_id = oc.id
LEFT JOIN incoming_connections ic2 ON cm.sender_id = ic2.id
LEFT JOIN outgoing_connections oc2 ON cm.receiver_id = oc2.id
LEFT JOIN files if2 ON ic2.file_id = if2.id
LEFT JOIN files of2 ON oc2.file_id = of2.id
WHERE (ic.file_id = ? OR oc.file_id = ?) AND cm.match_confidence > 0.5
ORDER BY cm.match_confidence DESC
LIMIT 15
"""
